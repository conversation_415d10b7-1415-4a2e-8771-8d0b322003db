/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  line-height: 1.6;
  color: #333;
  overflow-x: hidden;
}

.container {
  max-width: 1500px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 导航栏样式 */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
  margin: 0 auto;
  padding: 10px 5%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-right: auto;
  margin-left: -10px;
}

.logo-img {
  width: 60px;
  height: 60px;
}

.logo-text h2 {
  color: #2c5aa0;
  font-size: 32px;
  font-weight: bold;
  margin: 0;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 30px;
}

.nav-menu a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-menu a:hover {
  color: #2c5aa0;
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.hamburger span {
  width: 25px;
  height: 3px;
  background: #333;
  margin: 3px 0;
  transition: 0.3s;
}

/* 主页横幅 */
.hero {
  padding: 120px 0 80px;
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.85) 0%,
    rgba(118, 75, 162, 0.85) 100%
  );
  color: white;
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: "";
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: url("assets/浮光壁垒LOGO2.svg") center/contain no-repeat,
    linear-gradient(
      135deg,
      rgba(102, 126, 234, 0.1) 0%,
      rgba(118, 75, 162, 0.1) 100%
    );
  background-size: 80%, cover;
  filter: blur(8px) brightness(0.3);
  opacity: 0.6;
  z-index: -1;
}

.hero::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at center,
    transparent 0%,
    rgba(0, 0, 0, 0.1) 100%
  );
  backdrop-filter: blur(2px);
  z-index: 0;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  position: relative;
  z-index: 2;
}

.hero-content h1 {
  font-size: 3.5rem;
  font-weight: bold;
  margin-bottom: 20px;
  line-height: 1.2;
}

.hero-content h2 {
  font-size: 2rem;
  font-weight: 300;
  margin-bottom: 30px;
  opacity: 0.9;
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: 40px;
  opacity: 0.8;
  line-height: 1.8;
}

.hero-buttons {
  display: flex;
  gap: 20px;
}

.btn {
  padding: 15px 30px;
  border: none;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}

.btn-primary {
  background: #71dcee;
  color: white;
}

.btn-primary:hover {
  background: #ff5252;
  transform: translateY(-2px);
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.btn-secondary:hover {
  background: white;
  color: #667eea;
}

/* 英雄区域图形 */
.hero-graphic {
  position: relative;
  height: 400px;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 0;
  left: 0;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 100px;
  right: 50px;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 50px;
  left: 100px;
  animation-delay: 4s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 通用区域样式 */
.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 20px;
}

.section-header p {
  font-size: 1.2rem;
  color: #666;
}

/* 关于我们 */
.about {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.about-content {
  display: flex;
  flex-direction: column;
  gap: 60px;
}

.about-main {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
  margin-bottom: 60px;
}

.about-card {
  background: white;
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.about-card:hover {
  transform: translateY(-10px);
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.about-card h3 {
  font-size: 1.8rem;
  color: #2c5aa0;
  margin-bottom: 20px;
}

.about-card p {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #555;
}

.value-section {
  background: white;
  padding: 50px;
  border-radius: 25px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.value-section h3 {
  font-size: 2rem;
  color: #2c5aa0;
  text-align: center;
  margin-bottom: 40px;
}

.value-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.value-item {
  text-align: center;
  padding: 30px 20px;
  border-radius: 15px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  transition: transform 0.3s ease;
}

.value-item:hover {
  transform: translateY(-5px);
}

.value-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.value-item h4 {
  font-size: 1.3rem;
  color: #2c5aa0;
  margin-bottom: 10px;
}

.value-item p {
  color: #666;
  font-size: 1rem;
  line-height: 1.6;
}

/* 核心业务 */
.services {
  padding: 100px 0;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
}

.service-card {
  background: white;
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  text-align: center;
}

.service-card:hover {
  transform: translateY(-10px);
}

.service-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.service-card h3 {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 20px;
}

.service-card p {
  color: #666;
  line-height: 1.8;
  margin-bottom: 25px;
}

.service-card ul {
  list-style: none;
  text-align: left;
}

.service-card li {
  padding: 8px 0;
  color: #555;
  position: relative;
  padding-left: 20px;
}

.service-card li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #2c5aa0;
  font-weight: bold;
}

/* 产品介绍 */
.app-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.app-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.app-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  position: relative;
  z-index: 1;
}

.app-text h2 {
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.app-text h3 {
  font-size: 1.8rem;
  font-weight: 300;
  margin-bottom: 30px;
  opacity: 0.9;
}

.app-text p {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 40px;
  opacity: 0.8;
}

.app-modules {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 25px;
}

.module-item {
  text-align: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.module-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.module-item h4 {
  font-size: 1.2rem;
  margin-bottom: 10px;
}

.module-item p {
  font-size: 0.95rem;
  opacity: 0.8;
}

/* 手机模型 */
.phone-frame {
  width: 280px;
  height: auto;
  background: #333;
  border-radius: 25px;
  padding: 8px;
  margin: 0 auto;
  position: relative;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
}

.app-screenshot {
  width: 100%;
  height: auto;
  border-radius: 20px;
  display: block;
}

/* 联系我们 */
.contact {
  padding: 100px 0;
  background: #f8f9fa;
}

.contact-content {
  display: flex;
  justify-content: center;
}

.contact-info-center {
  text-align: center;
  max-width: 800px;
}

.company-logo {
  margin-bottom: 30px;
}

.contact-logo {
  width: 280px;
  height: 280px;
}

.contact-info-center h3 {
  font-size: 2rem;
  color: #2c5aa0;
  margin-bottom: 40px;
}

.contact-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 40px;
}

.contact-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 15px;
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  text-align: left;
}

.contact-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.contact-details strong {
  display: block;
  color: #333;
  font-size: 1.1rem;
  margin-bottom: 5px;
}

.contact-details span {
  color: #666;
  font-size: 1rem;
  line-height: 1.4;
}

.company-mission {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 20px;
  margin-top: 20px;
}

.company-mission p {
  font-size: 1.2rem;
  margin-bottom: 10px;
  opacity: 0.9;
}

.company-mission p:last-child {
  margin-bottom: 0;
  font-weight: 600;
}

/* 页脚 */
.footer {
  background: #333;
  color: white;
  padding: 0px 10px 20px 20px;
}

.footer-content {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}

.footer-center {
  text-align: center;
}

.footer-center h3 {
  margin-bottom: 20px;
  color: #2c5aa0;
  font-size: 1.8rem;
}

.footer-center p {
  color: #ccc;
  font-size: 1.1rem;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #555;
  color: #ccc;
}

/* 响应式设计 */
@media (max-width: 1024px) and (min-width: 769px) {
  .value-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .hamburger {
    display: flex;
  }

  .nav-menu {
    display: none;
  }

  .hero-container,
  .about-content,
  .app-content,
  .contact-content,
  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .hero-content h1 {
    font-size: 2.5rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .app-modules {
    grid-template-columns: 1fr;
  }

  .contact-row {
    grid-template-columns: 1fr;
  }

  .about-main {
    grid-template-columns: 1fr;
  }

  .value-grid {
    grid-template-columns: 1fr;
  }

  .phone-frame {
    width: 220px;
  }

  .floating-customer-service {
    right: 20px;
    bottom: 20px;
  }

  .customer-service-icon {
    padding: 10px 15px;
  }

  .service-text {
    font-size: 12px;
  }

  .customer-service-icon img {
    width: 25px;
    height: 25px;
    margin-right: 8px;
  }

  .modal-content {
    margin: 20% auto;
    padding: 20px;
    width: 95%;
  }

  .qr-code {
    width: 150px;
    height: 150px;
  }
}

/* 悬浮客服样式 */
.floating-customer-service {
  position: fixed;
  right: 30px;
  bottom: 30px;
  z-index: 1000;
  cursor: pointer;
}

.customer-service-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #2c5aa0;
  color: white;
  padding: 12px 16px;
  border-radius: 50px;
  box-shadow: 0 4px 20px rgba(44, 90, 160, 0.3);
  transition: all 0.3s ease;
  animation: pulse 2s infinite;
  min-width: 60px;
  height: 50px;
}

.customer-service-icon:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(44, 90, 160, 0.4);
  background: #1e3d72;
}

.customer-service-icon img {
  display: none;
}

.service-text {
  font-size: 16px;
  font-weight: 600;
  white-space: nowrap;
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 20px rgba(44, 90, 160, 0.3);
  }
  50% {
    box-shadow: 0 4px 20px rgba(44, 90, 160, 0.6);
  }
  100% {
    box-shadow: 0 4px 20px rgba(44, 90, 160, 0.3);
  }
}

/* 客服弹窗样式 */
.customer-service-modal {
  display: none;
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
}

.modal-content {
  background-color: white;
  margin: 10% auto;
  padding: 30px;
  border-radius: 15px;
  width: 90%;
  max-width: 400px;
  text-align: center;
  position: relative;
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.close-modal {
  position: absolute;
  right: 15px;
  top: 15px;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  color: #999;
  transition: color 0.3s ease;
}

.close-modal:hover {
  color: #333;
}

.modal-content h3 {
  margin-bottom: 20px;
  color: #333;
  font-size: 20px;
}

.qr-code {
  width: 200px;
  height: 200px;
  border-radius: 10px;
  margin: 20px 0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.modal-content p {
  color: #666;
  font-size: 14px;
  margin-top: 15px;
}
